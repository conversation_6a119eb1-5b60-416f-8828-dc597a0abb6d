/* pages/profile/profile.wxss */
.profile-container {
  height: 100vh;
  background-color: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 主要内容区域 */
.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.top-nav {
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  padding-top: calc(env(safe-area-inset-top) + 75rpx);
}

.nav-left, .nav-right {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: white;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

/* 用户信息区域 */
.user-section {
  position: relative;
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  padding: 48rpx 32rpx;
  padding-top: calc(env(safe-area-inset-top) + 120rpx);
  overflow: hidden;
  margin-top: 50rpx;
  flex-shrink: 0;
}

/* 背景装饰 */
.bg-circle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}

.bg-circle-1 {
  width: 256rpx;
  height: 256rpx;
  top: 0;
  right: 0;
  transform: translate(64rpx, -64rpx);
}

.bg-circle-2 {
  width: 192rpx;
  height: 192rpx;
  bottom: 0;
  left: 0;
  transform: translate(-48rpx, 48rpx);
}

.user-content {
  position: relative;
  z-index: 1;
}

/* 用户头像和基本信息 */
.user-header {
  display: flex;
  align-items: flex-start;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.user-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  display: block;
}

.user-stats {
  display: flex;
  gap: 48rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 个人简介 */
.user-bio {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.6;
}

/* 功能标签栏 */
.tabs-section {
  background: white;
  position: sticky;
  top: calc(env(safe-area-inset-top) + 88rpx);
  z-index: 90;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.tabs-row {
  display: flex;
}

.tab-item {
  flex: 1;
  padding: 32rpx 0;
  text-align: center;
  position: relative;
}

.tab-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
  transition: color 0.3s;
}

.tab-item.active .tab-text {
  color: #ef4444;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 64rpx;
  height: 4rpx;
  background: linear-gradient(to right, #ef4444, #ec4899);
  border-radius: 4rpx;
}

/* 活动状态筛选 */
.status-filter {
  background: white;
  padding: 24rpx 32rpx;
  border-bottom: 2rpx solid #f3f4f6;
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  gap: 16rpx;
}

.filter-item {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #6b7280;
  background: #f3f4f6;
  transition: all 0.3s;
}

.filter-item.active {
  color: white;
  background: linear-gradient(to right, #ef4444, #ec4899);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.2);
}

/* 活动列表滚动区域 */
.activities-scroll {
  flex: 1;
  overflow: hidden;
}

/* 活动列表 */
.activities-list {
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.activity-item {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #f3f4f6;
}

.activity-content {
  display: flex;
  gap: 24rpx;
}

.activity-cover {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  flex-shrink: 0;
}

.activity-info {
  flex: 1;
  min-width: 0;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  flex: 1;
  margin-right: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-type {
  padding: 8rpx 16rpx;
  background: #fee2e2;
  color: #ef4444;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 50rpx;
  flex-shrink: 0;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 24rpx;
  color: #9ca3af;
}

.detail-text {
  font-size: 24rpx;
  color: #6b7280;
} 