// pages/history/history.js
Page({
  data: {
    // 筛选标签
    historyFilters: [
      { key: 'all', label: '全部' },
      { key: 'recent', label: '最近3个月' },
      { key: 'half-year', label: '半年内' },
      { key: 'year', label: '一年内' },
      { key: 'participated', label: '我参与的' }
    ],
    activeFilter: 'all',
    
    // 历史活动数据
    historyActivities: [
      {
        month: '2024年7月',
        activities: [
          {
            id: 1,
            title: '《人类简史》读书分享会',
            date: '7月15日',
            time: '19:30-21:00',
            platform: '腾讯会议',
            participants: 45,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s27814883.jpg',
            rating: [1, 1, 1, 1, 1],
            score: '4.8'
          },
          {
            id: 2,
            title: '《三体》科幻文学讨论',
            date: '7月8日',
            time: '20:00-21:30',
            platform: '钉钉会议',
            participants: 38,
            coverImage: 'https://img9.doubanio.com/view/subject/l/public/s2768378.jpg',
            rating: [1, 1, 1, 1, 1],
            score: '4.9'
          }
        ]
      },
      {
        month: '2024年6月',
        activities: [
          {
            id: 3,
            title: '《百年孤独》魔幻现实主义探讨',
            date: '6月28日',
            time: '19:00-20:30',
            platform: '腾讯会议',
            participants: 52,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s6384944.jpg',
            rating: [1, 1, 1, 1, 0],
            score: '4.6'
          },
          {
            id: 4,
            title: '《活着》生命意义思辨',
            date: '6月21日',
            time: '20:00-21:00',
            platform: '钉钉会议',
            participants: 41,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s4913064.jpg',
            rating: [1, 1, 1, 1, 1],
            score: '4.7'
          },
          {
            id: 5,
            title: '《小王子》童话哲学分享',
            date: '6月14日',
            time: '19:30-20:30',
            platform: '腾讯会议',
            participants: 35,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s1237549.jpg',
            rating: [1, 1, 1, 1, 0],
            score: '4.5'
          }
        ]
      },
      {
        month: '2024年5月',
        activities: [
          {
            id: 6,
            title: '《围城》现代文学赏析',
            date: '5月30日',
            time: '20:00-21:30',
            platform: '钉钉会议',
            participants: 29,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s1070959.jpg',
            rating: [1, 1, 1, 1, 0],
            score: '4.4'
          },
          {
            id: 7,
            title: '《1984》反乌托邦文学讨论',
            date: '5月23日',
            time: '19:00-20:30',
            platform: '腾讯会议',
            participants: 47,
            coverImage: 'https://img3.doubanio.com/view/subject/l/public/s4371408.jpg',
            rating: [1, 1, 1, 1, 1],
            score: '4.8'
          }
        ]
      }
    ],
    
    hasMore: true
  },

  onLoad() {
    console.log('历史活动页面加载');
  },

  onShow() {
    console.log('历史活动页面显示');
  },

  // 切换筛选标签
  switchFilter(e) {
    const key = e.currentTarget.dataset.key;
    this.setData({
      activeFilter: key
    });
    
    // 根据筛选条件重新加载数据
    this.loadHistoryActivities(key);
  },

  // 加载历史活动数据
  loadHistoryActivities(filterKey = 'all') {
    console.log('加载历史活动数据，筛选条件：', filterKey);
    
    // 这里可以根据筛选条件调用不同的API
    // 暂时使用模拟数据
    
    wx.showLoading({
      title: '加载中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      // 根据筛选条件过滤数据的逻辑可以在这里实现
    }, 500);
  },

  // 查看活动详情
  viewActivityDetail(e) {
    const activityId = e.currentTarget.dataset.id;
    console.log('查看活动详情，ID：', activityId);
    
    wx.showToast({
      title: '查看活动详情',
      icon: 'none'
    });
    
    // 跳转到活动详情页面
    // wx.navigateTo({
    //   url: `/pages/activity-detail/activity-detail?id=${activityId}`
    // });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新历史活动');
    this.loadHistoryActivities(this.data.activeFilter);
    
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    if (!this.data.hasMore) {
      return;
    }
    
    console.log('加载更多历史活动');
    
    wx.showLoading({
      title: '加载中...'
    });
    
    // 模拟加载更多数据
    setTimeout(() => {
      wx.hideLoading();
      
      // 这里可以添加更多数据到 historyActivities 数组
      // 如果没有更多数据，设置 hasMore 为 false
      this.setData({
        hasMore: false
      });
      
      wx.showToast({
        title: '没有更多数据了',
        icon: 'none'
      });
    }, 1000);
  }
});
