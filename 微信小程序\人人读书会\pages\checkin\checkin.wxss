/* pages/checkin/checkin.wxss */
.checkin-container {
  height: 100vh;
  background-color: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 48rpx 32rpx;
  padding-top: calc(env(safe-area-inset-top) + 48rpx);
  overflow-y: auto;
}

/* 签到卡片 */
.checkin-card {
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  border-radius: 32rpx;
  margin-top: 10%;
  padding: 48rpx;
  margin-bottom: 48rpx;
  box-shadow: 0 8rpx 24rpx rgba(239, 68, 68, 0.2);
}

.checkin-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.checkin-title {
  display: block;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  margin-bottom: 16rpx;
}

.checkin-date {
  display: block;
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
}

.checkin-button-wrapper {
  text-align: center;
  margin-bottom: 48rpx;
}

.checkin-button {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background: white;
  color: #ef4444;
  font-size: 40rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  border: none;
  transition: all 0.3s;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.checkin-button.checked {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  cursor: not-allowed;
}

.check-icon {
  font-size: 48rpx;
}

.checkin-tip {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 统计信息 */
.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 32rpx;
  backdrop-filter: blur(10px);
}

.stats-item {
  text-align: center;
}

.stats-value {
  display: block;
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

/* 排行榜卡片 */
.ranking-card {
  background: white;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.ranking-header {
  margin-bottom: 32rpx;
}

.ranking-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 时间周期选择器 */
.period-selector {
  display: flex;
  background: #f3f4f6;
  border-radius: 50rpx;
  padding: 8rpx;
  margin-bottom: 32rpx;
}

.period-item {
  flex: 1;
  text-align: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: #6b7280;
  border-radius: 42rpx;
  transition: all 0.3s;
}

.period-item.active {
  background: white;
  color: #ef4444;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 排名列表 */
.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 24rpx;
}

.ranking-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.rank-badge {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-badge.rank-1 {
  background: #fbbf24;
  color: white;
}

.rank-badge.rank-2 {
  background: #d1d5db;
  color: #4b5563;
}

.rank-badge.rank-3 {
  background: #f97316;
  color: white;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

.user-name.highlight {
  color: #ef4444;
}

.checkin-days {
  font-size: 24rpx;
  color: #6b7280;
}

.points-info {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.medal-icon {
  font-size: 32rpx;
}

.points-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
}

/* 积分规则卡片 */
.rules-card {
  background: white;
  border-radius: 32rpx;
  margin-bottom: 25%;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.rules-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  display: block;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.rule-icon {
  font-size: 32rpx;
}

.rule-text {
  font-size: 28rpx;
  color: #6b7280;
} 