/* pages/history/history.wxss */
.history-container {
  height: 100vh;
  background-color: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
}

/* 页面标题 */
.page-header {
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  padding: 48rpx 32rpx;
  flex-shrink: 0;
}

.page-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}

/* 筛选标签 */
.filter-section {
  background: white;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  flex-shrink: 0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-container {
  display: inline-flex;
  gap: 48rpx;
  padding: 0 32rpx;
}

.filter-item {
  padding: 8rpx 24rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #6b7280;
  transition: all 0.3s;
  white-space: nowrap;
}

.filter-hover {
  background: #f3f4f6;
}

.filter-item.active {
  background: linear-gradient(90deg, #fef2f2 0%, #fdf2f8 100%);
  background: linear-gradient(90deg, #dc2626 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

/* 活动列表滚动区域 */
.activity-scroll {
  flex: 1;
  overflow: hidden;
}

/* 活动内容列表 */
.activity-content {
  background: white;
  padding: 40rpx 32rpx;
  padding-bottom: 120rpx;
}

/* 月份分组 */
.month-group {
  margin-bottom: 48rpx;
}

.month-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f3f4f6;
}

.month-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
}

.count-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 活动卡片列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.activity-item {
  display: flex;
  background: #f9fafb;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.activity-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.activity-cover-wrapper {
  width: 160rpx;
  height: 224rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.activity-cover {
  width: 100%;
  height: 100%;
}

.finished-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.activity-info {
  flex: 1;
  margin-left: 32rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 24rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 活动评分 */
.activity-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.rating-label {
  font-size: 24rpx;
  color: #6b7280;
}

.stars {
  display: flex;
  gap: 2rpx;
}

.star {
  font-size: 24rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #f59e0b;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-text {
  font-size: 28rpx;
  color: #6b7280;
}

.no-more {
  text-align: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #9ca3af;
  display: block;
}
