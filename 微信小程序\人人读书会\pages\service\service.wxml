<!--pages/service/service.wxml-->
<view class="service-container" style="overflow: hidden;">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 轮播图 -->
    <view class="carousel-section">
      <view class="carousel-container">
        <view class="carousel-wrapper">
          <image 
            class="carousel-image" 
            src="{{slides[currentSlide].image}}" 
            mode="aspectFill"
          />
          
          <!-- 轮播内容覆盖层 -->
          <view class="carousel-overlay">
            <view class="carousel-content">
              <text class="carousel-title">{{slides[currentSlide].title}}</text>
              <text class="carousel-subtitle">{{slides[currentSlide].subtitle}}</text>
              <text class="carousel-description">{{slides[currentSlide].description}}</text>
            </view>
          </view>

          <!-- 轮播控制按钮 -->
          <view class="carousel-controls">
            <view class="control-btn prev-btn" bindtap="prevSlide">
              <text class="control-icon">‹</text>
            </view>
            
            <view class="control-btn next-btn" bindtap="nextSlide">
              <text class="control-icon">›</text>
            </view>
          </view>
        </view>

        <!-- 轮播指示器 -->
        <view class="carousel-indicators">
          <view 
            wx:for="{{slides}}" 
            wx:key="index"
            class="indicator {{currentSlide === index ? 'active' : ''}}"
            data-index="{{index}}"
            bindtap="onSlideIndicator"
          ></view>
        </view>
      </view>
    </view>

    <!-- 使用教程标题 -->
    <view class="section-header">
      <text class="section-title">创始人服务会列表</text>
      <text class="section-subtitle">帮助您快速上手平台功能</text>
    </view>

    <!-- 教程列表 -->
    <scroll-view scroll-y class="tutorial-scroll" enhanced show-scrollbar="{{false}}">
      <view class="tutorial-list">
        <view 
          wx:for="{{tutorials}}" 
          wx:key="index"
          class="tutorial-item"
          data-index="{{index}}"
          bindtap="viewTutorial"
          hover-class="tutorial-hover"
        >
          <view class="tutorial-content">
            <!-- 左侧图片 -->
            <view class="tutorial-image-wrapper">
              <image class="tutorial-image" src="{{item.image}}" mode="aspectFill" />
            </view>
            
            <!-- 右侧内容 -->
            <view class="tutorial-info">
              <view class="tutorial-header">
                <view class="tutorial-text">
                  <text class="tutorial-title">{{item.title}}</text>
                  <text class="tutorial-subtitle">{{item.subtitle}}</text>
                  
                  <!-- 状态标签 -->
                  <view class="status-tag {{item.status === '线上' ? 'online' : 'offline'}}">
                    {{item.tag}}
                  </view>
                </view>
                
                <!-- 右侧箭头 -->
                <view class="tutorial-arrow">
                  <text class="arrow-icon">›</text>
                </view>
              </view>
              
              <!-- 详细信息 -->
              <view class="tutorial-details">
                <view class="detail-row">
                  <text class="detail-icon">⏰</text>
                  <text class="detail-text">时间：{{item.time}}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-icon">📍</text>
                  <text class="detail-text">平台：{{item.location}}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-icon">📅</text>
                  <text class="detail-text">报名：{{item.schedule}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view> 