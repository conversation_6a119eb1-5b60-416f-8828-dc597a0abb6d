// pages/service/service.js
Page({
  data: {
    currentSlide: 0,
    slides: [
      {
        title: '平台操作指南',
        subtitle: '掌握平台核心功能使用方法',
        image: 'https://readdy.ai/api/search-image?query=Modern%20mobile%20app%20interface%20tutorial%2C%20clean%20design%2C%20gradient%20background%2C%20user-friendly%20layout%2C%20step-by-step%20guide%20visualization%2C%20soft%20lighting%2C%20professional%20look&width=320&height=180&seq=slide1&orientation=landscape',
        description: '详细介绍平台各项功能的使用方法'
      },
      {
        title: '新手入门教程',
        subtitle: '快速上手平台基础操作',
        image: 'https://readdy.ai/api/search-image?query=Beginner%20tutorial%20interface%2C%20step-by-step%20onboarding%2C%20clean%20modern%20design%2C%20gradient%20background%2C%20user%20guide%20elements%2C%20soft%20lighting%2C%20professional%20presentation&width=320&height=180&seq=slide2&orientation=landscape',
        description: '从零开始，轻松掌握平台使用技巧'
      },
      {
        title: '高级功能介绍',
        subtitle: '解锁更多强大功能',
        image: 'https://readdy.ai/api/search-image?query=Advanced%20features%20showcase%2C%20modern%20interface%20design%2C%20gradient%20background%2C%20professional%20layout%2C%20feature%20highlights%2C%20clean%20presentation%2C%20soft%20lighting&width=320&height=180&seq=slide3&orientation=landscape',
        description: '深度了解平台的高级功能特性'
      }
    ],
    tutorials: [
      {
        image: 'https://readdy.ai/api/search-image?query=Modern%20book%20cover%20design%2C%20reading%20tutorial%20guide%2C%20clean%20minimalist%20layout%2C%20professional%20presentation%2C%20soft%20gradient%20background%2C%20educational%20content%2C%20step-by-step%20guide%20visualization&width=280&height=160&seq=tutorial1&orientation=landscape',
        title: '乔布斯传与穿越',
        subtitle: '一部读书会2025年第30期线上',
        status: '线上',
        time: '07/25 20:00',
        location: '腾讯会议',
        schedule: '10/11',
        tag: '线上'
      },
      {
        image: 'https://readdy.ai/api/search-image?query=Business%20book%20cover%20design%2C%20entrepreneur%20biography%2C%20clean%20professional%20layout%2C%20modern%20design%2C%20soft%20lighting%2C%20educational%20content%2C%20reading%20guide%20presentation&width=280&height=160&seq=tutorial2&orientation=landscape',
        title: '创业思维与实践',
        subtitle: '企业家精神读书分享会',
        status: '线上',
        time: '07/28 19:30',
        location: '腾讯会议',
        schedule: '08/15',
        tag: '线上'
      },
      {
        image: 'https://readdy.ai/api/search-image?query=Leadership%20book%20cover%20design%2C%20management%20guide%2C%20professional%20layout%2C%20clean%20design%2C%20soft%20gradient%20background%2C%20business%20education%20content&width=280&height=160&seq=tutorial3&orientation=landscape',
        title: '领导力提升指南',
        subtitle: '管理者必读书籍解读',
        status: '线下',
        time: '08/02 14:00',
        location: '文化中心',
        schedule: '08/20',
        tag: '线下'
      },
      {
        image: 'https://readdy.ai/api/search-image?query=Psychology%20book%20cover%20design%2C%20self-improvement%20guide%2C%20modern%20clean%20layout%2C%20educational%20content%2C%20soft%20lighting%2C%20professional%20presentation&width=280&height=160&seq=tutorial4&orientation=landscape',
        title: '心理学与生活',
        subtitle: '探索内心世界的奥秘',
        status: '线上',
        time: '08/05 20:00',
        location: '腾讯会议',
        schedule: '08/25',
        tag: '线上'
      }
    ]
  },

  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
    
    // 开始自动轮播
    this.startAutoSlide();
  },

  onHide() {
    // 停止自动轮播
    if (this.slideTimer) {
      clearInterval(this.slideTimer);
    }
  },

  onUnload() {
    // 清除定时器
    if (this.slideTimer) {
      clearInterval(this.slideTimer);
    }
  },

  // 开始自动轮播
  startAutoSlide() {
    this.slideTimer = setInterval(() => {
      this.nextSlide();
    }, 5000);
  },

  // 下一张轮播图
  nextSlide() {
    const nextIndex = (this.data.currentSlide + 1) % this.data.slides.length;
    this.setData({
      currentSlide: nextIndex
    });
  },

  // 上一张轮播图
  prevSlide() {
    const prevIndex = (this.data.currentSlide - 1 + this.data.slides.length) % this.data.slides.length;
    this.setData({
      currentSlide: prevIndex
    });
  },

  // 点击轮播指示器
  onSlideIndicator(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentSlide: index
    });
  },

  // 查看教程详情
  viewTutorial(e) {
    const index = e.currentTarget.dataset.index;
    const tutorial = this.data.tutorials[index];
    
    wx.showModal({
      title: tutorial.title,
      content: `${tutorial.subtitle}\n\n时间：${tutorial.time}\n平台：${tutorial.location}\n报名：${tutorial.schedule}`,
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/dashboard/dashboard'
    });
  }
}); 