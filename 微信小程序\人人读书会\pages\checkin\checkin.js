// pages/checkin/checkin.js
Page({
  data: {
    currentStreak: 7,
    totalDays: 85,
    totalPoints: 850,
    hasCheckedToday: false,
    activeRankingPeriod: 'week',
    currentDate: '',
    rankingPeriods: [
      { key: 'week', label: '周榜' },
      { key: 'month', label: '月榜' },
      { key: 'quarter', label: '季榜' }
    ],
    weekData: [
      { rank: 1, name: '李小明', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar1&orientation=squarish', days: 7, points: 70 },
      { rank: 2, name: '王美丽', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar2&orientation=squarish', days: 6, points: 60 },
      { rank: 3, name: '张强', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20confident%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar3&orientation=squarish', days: 6, points: 60 },
      { rank: 4, name: '我', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20person%20portrait%2C%20business%20casual%2C%20warm%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar4&orientation=squarish', days: 5, points: 50 },
      { rank: 5, name: '刘芳', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20bright%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar5&orientation=squarish', days: 4, points: 40 }
    ],
    monthData: [
      { rank: 1, name: '王美丽', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar2&orientation=squarish', days: 28, points: 280 },
      { rank: 2, name: '李小明', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar1&orientation=squarish', days: 26, points: 260 },
      { rank: 3, name: '张强', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20confident%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar3&orientation=squarish', days: 25, points: 250 },
      { rank: 4, name: '刘芳', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20bright%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar5&orientation=squarish', days: 23, points: 230 },
      { rank: 5, name: '我', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20person%20portrait%2C%20business%20casual%2C%20warm%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar4&orientation=squarish', days: 20, points: 200 }
    ],
    quarterData: [
      { rank: 1, name: '李小明', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar1&orientation=squarish', days: 85, points: 850 },
      { rank: 2, name: '王美丽', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20friendly%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar2&orientation=squarish', days: 82, points: 820 },
      { rank: 3, name: '我', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20person%20portrait%2C%20business%20casual%2C%20warm%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar4&orientation=squarish', days: 78, points: 780 },
      { rank: 4, name: '张强', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20male%20portrait%2C%20business%20casual%2C%20confident%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar3&orientation=squarish', days: 75, points: 750 },
      { rank: 5, name: '刘芳', avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20business%20casual%2C%20bright%20smile%2C%20clean%20background%2C%20high%20quality%20headshot&width=60&height=60&seq=avatar5&orientation=squarish', days: 72, points: 720 }
    ],
    currentRankingData: []
  },

  onLoad() {
    // 设置当前日期
    const date = new Date();
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekday = weekdays[date.getDay()];
    
    this.setData({
      currentDate: `${month}月${day}日 ${weekday}`,
      currentRankingData: this.data.weekData
    });
  },

  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      });
    }
  },

  // 签到
  handleCheckin() {
    if (!this.data.hasCheckedToday) {
      this.setData({
        hasCheckedToday: true,
        currentStreak: this.data.currentStreak + 1,
        totalDays: this.data.totalDays + 1,
        totalPoints: this.data.totalPoints + 10
      });

      wx.showToast({
        title: '签到成功',
        icon: 'success'
      });
    }
  },

  // 切换排行榜时间周期
  switchRankingPeriod(e) {
    const period = e.currentTarget.dataset.period;
    let rankingData;

    switch (period) {
      case 'week':
        rankingData = this.data.weekData;
        break;
      case 'month':
        rankingData = this.data.monthData;
        break;
      case 'quarter':
        rankingData = this.data.quarterData;
        break;
      default:
        rankingData = this.data.weekData;
    }

    this.setData({
      activeRankingPeriod: period,
      currentRankingData: rankingData
    });
  },

  // 查看帮助
  showHelp() {
    wx.showModal({
      title: '签到规则',
      content: '1. 每日签到获得10积分\n2. 连续签到7天额外获得20积分\n3. 月度排行榜前3名获得奖励积分',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
}); 