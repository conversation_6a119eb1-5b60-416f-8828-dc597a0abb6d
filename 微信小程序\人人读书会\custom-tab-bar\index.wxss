/* custom-tab-bar/index.wxss */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: white;
  display: flex;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.tab-bar-border {
  background-color: #f3f4f6;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1rpx;
  transform: scaleY(0.5);
}

.tab-bar-item {
  flex: 1;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
}

.tab-bar-item.active {
  transform: scale(1.05);
}

.tab-bar-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.tab-bar-text {
  font-size: 20rpx;
  font-weight: 500;
}

/* 发布按钮样式 */
.publish-button-wrapper {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.publish-button {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(239, 68, 68, 0.4);
  transition: all 0.3s;
}

.publish-button:active {
  transform: scale(0.95);
}

.publish-icon {
  color: white;
  font-size: 48rpx;
  font-weight: 300;
  line-height: 1;
} 