<!--pages/checkin/checkin.wxml-->
<view class="checkin-container" style="overflow: hidden;">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 签到卡片 -->
    <view class="checkin-card">
      <view class="checkin-header">
        <text class="checkin-title">今日签到</text>
        <text class="checkin-date">{{currentDate}}</text>
      </view>
      
      <view class="checkin-button-wrapper">
        <button 
          class="checkin-button {{hasCheckedToday ? 'checked' : ''}}" 
          bindtap="handleCheckin"
          disabled="{{hasCheckedToday}}"
        >
          <text wx:if="{{hasCheckedToday}}" class="check-icon">✓</text>
          <text wx:else>签到</text>
        </button>
        
        <text class="checkin-tip">{{hasCheckedToday ? '今日已签到' : '点击签到获得10积分'}}</text>
      </view>

      <!-- 统计信息 -->
      <view class="stats-container">
        <view class="stats-item">
          <text class="stats-value">{{currentStreak}}</text>
          <text class="stats-label">连续签到</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{totalDays}}</text>
          <text class="stats-label">累计天数</text>
        </view>
        <view class="stats-item">
          <text class="stats-value">{{totalPoints}}</text>
          <text class="stats-label">总积分</text>
        </view>
      </view>
    </view>

    <!-- 排行榜 -->
    <view class="ranking-card">
      <view class="ranking-header">
        <text class="ranking-title">签到排行榜</text>
      </view>
      
      <!-- 时间周期选择 -->
      <view class="period-selector">
        <view 
          wx:for="{{rankingPeriods}}" 
          wx:key="key"
          class="period-item {{activeRankingPeriod === item.key ? 'active' : ''}}"
          data-period="{{item.key}}"
          bindtap="switchRankingPeriod"
        >
          {{item.label}}
        </view>
      </view>

      <!-- 排名列表 -->
      <view class="ranking-list">
        <view 
          wx:for="{{currentRankingData}}" 
          wx:key="rank" 
          class="ranking-item"
        >
          <view class="ranking-info">
            <!-- 排名徽章 -->
            <view class="rank-badge {{item.rank <= 3 ? 'rank-' + item.rank : ''}}">
              {{item.rank}}
            </view>
            
            <!-- 用户头像 -->
            <image class="user-avatar" src="{{item.avatar}}" mode="aspectFill" />
            
            <!-- 用户信息 -->
            <view class="user-info">
              <text class="user-name {{item.name === '我' ? 'highlight' : ''}}">{{item.name}}</text>
              <text class="checkin-days">{{item.days}}天签到</text>
            </view>
          </view>
          
          <!-- 积分 -->
          <view class="points-info">
            <text class="medal-icon">🏅</text>
            <text class="points-value">{{item.points}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 积分说明 -->
    <view class="rules-card">
      <text class="rules-title">积分规则</text>
      <view class="rules-list">
        <view class="rule-item">
          <text class="rule-icon">✓</text>
          <text class="rule-text">每日签到获得10积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">🔥</text>
          <text class="rule-text">连续签到7天额外获得20积分</text>
        </view>
        <view class="rule-item">
          <text class="rule-icon">🏆</text>
          <text class="rule-text">月度排行榜前3名获得奖励积分</text>
        </view>
      </view>
    </view>
  </view>
</view> 