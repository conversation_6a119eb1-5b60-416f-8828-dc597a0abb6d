<!--custom-tab-bar/index.wxml-->
<view class="tab-bar">
  <view class="tab-bar-border"></view>
  <view 
    wx:for="{{list}}" 
    wx:key="index" 
    class="tab-bar-item {{selected === index ? 'active' : ''}}"
    data-path="{{item.pagePath}}"
    data-index="{{index}}"
    bindtap="switchTab"
  >
    <image 
      src="{{selected === index ? item.selectedIconPath : item.iconPath}}" 
      class="tab-bar-icon"
    />
    <view 
      style="color: {{selected === index ? selectedColor : color}}"
      class="tab-bar-text"
    >
      {{item.text}}
    </view>
  </view>
  
  <!-- 中间的发布按钮 -->
  <view class="publish-button-wrapper">
    <view class="publish-button" bindtap="onPublish">
      <text class="publish-icon">+</text>
    </view>
  </view>
</view> 