/* pages/dashboard/dashboard.wxss */
.dashboard-container {
  height: 100vh;
  background-color: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
}

/* 轮播图区域 */
.swiper-section {
  flex-shrink: 0;
  margin-bottom: 0;
}

.banner-swiper {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
  display: block;
}

/* 功能卡片区域 */
.feature-section {
  background: linear-gradient(135deg, #ef4444 0%, #ec4899 100%);
  padding: 48rpx 32rpx;
  flex-shrink: 0;
}

.feature-grid {
  display: flex;
  gap: 24rpx;
}

.feature-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.card-hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.vip-icon {
  background: linear-gradient(135deg, #f87171 0%, #ec4899 100%);
}

.activity-icon {
  background: linear-gradient(135deg, #f87171 0%, #ec4899 100%);
}

.branch-icon {
  background: linear-gradient(135deg, #f87171 0%, #ec4899 100%);
}

.icon-text {
  font-size: 36rpx;
  color: white;
}

.feature-text {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}

/* 活动时间分类 */
.time-filter-section {
  background: white;
  padding: 40rpx 32rpx;
  border-radius: 48rpx 48rpx 0 0;
  margin-top: -32rpx;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.filter-container {
  display: flex;
  background: #f3f4f6;
  border-radius: 50rpx;
  padding: 8rpx;
  gap: 8rpx;
}

.time-filter-item {
  flex: 1;
  padding: 32rpx 16rpx;
  border-radius: 42rpx;
  position: relative;
  transition: all 0.2s;
}

.time-filter-item.active {
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.filter-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.filter-icon {
  font-size: 32rpx;
}

.filter-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
}

.time-filter-item.active .filter-text {
  background: linear-gradient(90deg, #dc2626 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.active-dot {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 16rpx;
  height: 16rpx;
  background: linear-gradient(90deg, #ef4444 0%, #ec4899 100%);
  border-radius: 50%;
}

/* 内容筛选标签 */
.content-filter-section {
  background: white;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  flex-shrink: 0;
}

.content-filter-scroll {
  white-space: nowrap;
}

.content-filter-container {
  display: inline-flex;
  gap: 48rpx;
  padding: 0 32rpx;
}

.content-filter-item {
  padding: 8rpx 24rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #6b7280;
  transition: all 0.3s;
  white-space: nowrap;
}

.filter-hover {
  background: #f3f4f6;
}

.content-filter-item.active {
  background: linear-gradient(90deg, #fef2f2 0%, #fdf2f8 100%);
  background: linear-gradient(90deg, #dc2626 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 500;
}

/* 活动列表滚动区域 */
.activity-scroll {
  flex: 1;
  overflow: hidden;
}

/* 活动内容列表 */
.activity-content {
  background: white;
  padding: 40rpx 32rpx;
  padding-bottom: 120rpx;
}

.date-header {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.date-number {
  font-size: 72rpx;
  font-weight: bold;
  color: #1f2937;
}

.date-info {
  margin-left: 16rpx;
}

.date-text {
  font-size: 28rpx;
  color: #6b7280;
}

/* 活动卡片列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.activity-item {
  display: flex;
  background: #f9fafb;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.activity-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.activity-cover-wrapper {
  width: 160rpx;
  height: 224rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
}

.activity-cover {
  width: 100%;
  height: 100%;
}

.activity-info {
  flex: 1;
  margin-left: 32rpx;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.5;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.activity-status-badge {
  display: inline-block;
  background: linear-gradient(90deg, #fef2f2 0%, #fdf2f8 100%);
  background: linear-gradient(90deg, #dc2626 0%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  border: 1rpx solid #fecaca;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 24rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #6b7280;
} 