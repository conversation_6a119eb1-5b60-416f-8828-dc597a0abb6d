// pages/login/login.js
Page({
  data: {
    isLoading: false,
    canIUseGetUserProfile: false,
    canIUseOpenData: false
  },

  onLoad() {
    // 检查是否支持getUserProfile接口
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    } else if (wx.canIUse('open-data.type.userAvatarUrl')) {
      this.setData({
        canIUseOpenData: true
      })
    }
  },

  // 微信登录
  handleWechatLogin() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });

    // 使用新版getUserProfile接口
    if (this.data.canIUseGetUserProfile) {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          console.log('获取用户信息成功', res);
          this.doLogin(res.userInfo);
        },
        fail: (err) => {
          console.error('获取用户信息失败', err);
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      });
    } else {
      // 兼容旧版本
      wx.getUserInfo({
        success: (res) => {
          this.doLogin(res.userInfo);
        },
        fail: (err) => {
          wx.showToast({
            title: '需要授权才能登录',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      });
    }
  },

  // 执行登录
  doLogin(userInfo) {
    wx.login({
      success: (res) => {
        if (res.code) {
          // 发送 res.code 到后台换取 openId, sessionKey, unionId
          console.log('登录凭证：', res.code);
          
          // 模拟登录成功，实际项目中需要调用后端接口
          setTimeout(() => {
            // 保存用户信息到本地
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('isLogin', true);
            
            wx.showToast({
              title: '登录成功',
              icon: 'success',
              duration: 1500
            });

            // 跳转到首页
            setTimeout(() => {
              wx.switchTab({
                url: '/pages/dashboard/dashboard'
              });
            }, 1500);
            
            this.setData({ isLoading: false });
          }, 2000);
        } else {
          console.error('登录失败！' + res.errMsg);
          wx.showToast({
            title: '登录失败',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (err) => {
        console.error('wx.login失败', err);
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议内容...',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策内容...',
      showCancel: false,
      confirmText: '我知道了'
    });
  }
}); 