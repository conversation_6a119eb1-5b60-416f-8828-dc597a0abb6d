<!--pages/history/history.wxml-->
<view class="history-container">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">历史活动</text>
      <text class="page-subtitle">回顾精彩的读书活动</text>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-section">
      <scroll-view scroll-x class="filter-scroll" show-scrollbar="{{false}}">
        <view class="filter-container">
          <view 
            wx:for="{{historyFilters}}" 
            wx:key="key"
            class="filter-item {{activeFilter === item.key ? 'active' : ''}}"
            data-key="{{item.key}}"
            bindtap="switchFilter"
            hover-class="filter-hover"
          >
            {{item.label}}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 历史活动列表 -->
    <scroll-view scroll-y class="activity-scroll" enhanced show-scrollbar="{{false}}">
      <view class="activity-content">
        <!-- 月份分组 -->
        <view wx:for="{{historyActivities}}" wx:key="month" class="month-group">
          <!-- 月份标题 -->
          <view class="month-header">
            <text class="month-text">{{item.month}}</text>
            <text class="count-text">{{item.activities.length}}场活动</text>
          </view>

          <!-- 活动列表 -->
          <view class="activity-list">
            <view 
              wx:for="{{item.activities}}" 
              wx:key="id" 
              wx:for-item="activity"
              class="activity-item" 
              data-id="{{activity.id}}" 
              bindtap="viewActivityDetail"
              hover-class="activity-hover"
            >
              <!-- 活动封面 -->
              <view class="activity-cover-wrapper">
                <image class="activity-cover" src="{{activity.coverImage}}" mode="aspectFill" />
                <!-- 已结束标识 -->
                <view class="finished-badge">已结束</view>
              </view>

              <!-- 活动信息 -->
              <view class="activity-info">
                <text class="activity-title">{{activity.title}}</text>

                <view class="activity-details">
                  <view class="detail-row">
                    <text class="detail-icon">📅</text>
                    <text class="detail-text">{{activity.date}}</text>
                  </view>

                  <view class="detail-row">
                    <text class="detail-icon">⏰</text>
                    <text class="detail-text">{{activity.time}}</text>
                  </view>

                  <view class="detail-row">
                    <text class="detail-icon">📹</text>
                    <text class="detail-text">{{activity.platform}}</text>
                  </view>

                  <view class="detail-row">
                    <text class="detail-icon">👥</text>
                    <text class="detail-text">参与人数：{{activity.participants}}</text>
                  </view>
                </view>

                <!-- 活动评分 -->
                <view class="activity-rating">
                  <text class="rating-label">活动评分：</text>
                  <view class="stars">
                    <text wx:for="{{activity.rating}}" wx:key="*this" class="star">⭐</text>
                  </view>
                  <text class="rating-score">{{activity.score}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" wx:if="{{hasMore}}">
          <text class="load-text">加载更多历史活动...</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" wx:if="{{!hasMore && historyActivities.length > 0}}">
          <text class="no-more-text">没有更多活动了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{historyActivities.length === 0}}">
          <text class="empty-icon">📚</text>
          <text class="empty-text">暂无历史活动</text>
          <text class="empty-desc">快去参加一些精彩的读书活动吧！</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
