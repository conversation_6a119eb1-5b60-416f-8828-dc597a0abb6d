// pages/profile/profile.js
Page({
  data: {
    activeTab: 'activity',
    activeStatus: 'pending',
    tabs: [
      { key: 'activity', label: '活动' },
      { key: 'creation', label: '创作' },
      { key: 'collection', label: '收藏' }
    ],
    statusTabs: [
      { key: 'pending', label: '待参加' },
      { key: 'joined', label: '已参加' },
      { key: 'created', label: '已发起' },
      { key: 'review', label: '待审核' }
    ],
    activities: [
      {
        id: 1,
        title: '《百年孤独》读书分享会',
        type: '线上',
        time: '08/01 20:00 周五',
        platform: '腾讯会议',
        participants: '1/13',
        cover: 'https://readdy.ai/api/search-image?query=book%20reading%20session%2C%20modern%20literature%2C%20cozy%20atmosphere%2C%20warm%20lighting%2C%20books%20on%20table%2C%20soft%20colors%2C%20elegant%20composition&width=80&height=80&seq=activity1&orientation=squarish'
      },
      {
        id: 2,
        title: '诗词朗诵交流会',
        type: '线上',
        time: '08/03 19:30 周日',
        platform: '钉钉会议',
        participants: '5/20',
        cover: 'https://readdy.ai/api/search-image?query=poetry%20recital%2C%20classical%20literature%2C%20microphone%2C%20elegant%20setting%2C%20soft%20lighting%2C%20cultural%20atmosphere&width=80&height=80&seq=activity2&orientation=squarish'
      },
      {
        id: 3,
        title: '现代文学作品讨论',
        type: '线上',
        time: '08/05 14:00 周二',
        platform: '腾讯会议',
        participants: '8/15',
        cover: 'https://readdy.ai/api/search-image?query=modern%20literature%20discussion%2C%20contemporary%20books%2C%20intellectual%20atmosphere%2C%20clean%20background%2C%20professional%20setting&width=80&height=80&seq=activity3&orientation=squarish'
      },
      {
        id: 4,
        title: '写作技巧分享课',
        type: '线上',
        time: '08/07 16:00 周四',
        platform: '钉钉会议',
        participants: '12/25',
        cover: 'https://readdy.ai/api/search-image?query=writing%20workshop%2C%20creative%20writing%2C%20notebook%20and%20pen%2C%20inspiring%20workspace%2C%20soft%20natural%20light&width=80&height=80&seq=activity4&orientation=squarish'
      }
    ],
    userInfo: {
      avatar: 'https://readdy.ai/api/search-image?query=professional%20asian%20female%20portrait%2C%20elegant%20appearance%2C%20warm%20smile%2C%20literary%20atmosphere%2C%20high%20quality%20headshot%2C%20soft%20lighting&width=64&height=64&seq=userAvatar&orientation=squarish',
      nickname: '时光叙事者',
      likes: 2,
      credits: 60,
      bio: '爱好朗读，读书，喜咖啡，好小酒。品人生百味，看百态人生。'
    }
  },

  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },

  // 切换主标签
  switchTab(e) {
    const key = e.currentTarget.dataset.key;
    this.setData({
      activeTab: key
    });
  },

  // 切换状态标签
  switchStatus(e) {
    const key = e.currentTarget.dataset.key;
    this.setData({
      activeStatus: key
    });
  },

  // 返回首页
  goBack() {
    wx.switchTab({
      url: '/pages/dashboard/dashboard'
    });
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '春天的朗读书会',
      path: '/pages/profile/profile'
    };
  },

  // 查看活动详情
  viewActivity(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '查看活动' + id,
      icon: 'none'
    });
  }
}); 