/* pages/service/service.wxss */
.service-container {
  height: 100vh;
  background-color: #f9fafb;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.main-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: env(safe-area-inset-top);
}

/* 轮播图区域 */
.carousel-section {
  padding: 32rpx;
  margin-bottom: 32rpx;
  margin-top: 20%;
  flex-shrink: 0;
}

.carousel-container {
  background: white;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.carousel-wrapper {
  position: relative;
  height: 448rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.carousel-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: flex-end;
}

.carousel-content {
  padding: 48rpx;
  color: white;
}

.carousel-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.carousel-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 8rpx;
}

.carousel-description {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 轮播控制按钮 */
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;
  pointer-events: none;
}

.control-btn {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s;
  pointer-events: auto;
}

.control-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

.control-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 16rpx;
}

.indicator {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s;
}

.indicator.active {
  background: white;
  transform: scale(1.2);
}

/* 使用教程标题 */
.section-header {
  padding: 0 32rpx 24rpx;
  flex-shrink: 0;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.section-subtitle {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
}

/* 教程列表滚动区域 */
.tutorial-scroll {
  flex: 1;
  overflow: hidden;
  padding-bottom: 120rpx;
}

/* 教程列表 */
.tutorial-list {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

/* 调整内容区域以适应更大的图片 */
.tutorial-item {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s;
  padding: 24rpx;
}

.tutorial-hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tutorial-content {
  display: flex;
  gap: 24rpx;
}

.tutorial-image-wrapper {
  width: 240rpx;
  height: 240rpx;
  flex-shrink: 0;
  overflow: hidden;
  border-radius: 16rpx;
}

.tutorial-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tutorial-info {
  flex: 1;
  padding: 0;
}

.tutorial-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tutorial-text {
  flex: 1;
  min-width: 0;
}

.tutorial-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tutorial-subtitle {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 状态标签 */
.status-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.status-tag.online {
  background: #fef2f2;
  color: #dc2626;
}

.status-tag.offline {
  background: #eff6ff;
  color: #2563eb;
}

.tutorial-arrow {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.arrow-icon {
  color: #d1d5db;
  font-size: 24rpx;
  font-weight: bold;
}

/* 详细信息 */
.tutorial-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-icon {
  font-size: 20rpx;
}

.detail-text {
  font-size: 20rpx;
  color: #6b7280;
} 