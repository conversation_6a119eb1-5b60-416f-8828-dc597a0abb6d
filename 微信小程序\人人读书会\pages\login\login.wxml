<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 渐变背景 -->
  <view class="gradient-bg"></view>
  
  <!-- 页面内容 -->
  <view class="login-content">


    <!-- 登录卡片 -->
    <view class="login-card">
        <!-- Logo和标题区域 -->
        <view class="logo-section">
      <view class="logo-wrapper">
        <image 
          class="logo-image" 
          src="https://readdy.ai/api/search-image?query=elegant%20book%20club%20logo%20with%20graduation%20cap%20and%20books%2C%20chinese%20traditional%20style%2C%20scholarly%20atmosphere%2C%20golden%20and%20blue%20colors%2C%20centered%20composition%2C%20minimalist%20design%2C%20professional%20look%2C%20white%20background%2C%20high%20quality&width=120&height=120&seq=reading-club-logo&orientation=squarish"
          mode="aspectFill"
        />
      </view>
      <text class="app-title">人人秀才读书会</text>
      <text class="app-subtitle">知识分享，共同成长</text>
    </view>
      <!-- 微信登录按钮 -->
      <button 
        class="wechat-login-btn {{isLoading ? 'loading' : ''}}" 
        bindtap="handleWechatLogin"
        disabled="{{isLoading}}"
      
      >
        <view class="btn-content">
          <image 
            wx:if="{{!isLoading}}"
            class="wechat-icon" 
            src="/images/wechat.svg"
            mode="aspectFit"
          />
          <view wx:if="{{isLoading}}" class="loading-spinner"></view>
          <text class="btn-text">{{isLoading ? '登录中...' : '微信一键登录'}}</text>
        </view>
      </button>

      <!-- 协议说明 -->
      <view class="agreement-section">
        <text class="agreement-text">登录即表示同意</text>
        <text class="agreement-link" bindtap="viewUserAgreement">用户协议</text>
        <text class="agreement-text">和</text>
        <text class="agreement-link" bindtap="viewPrivacyPolicy">隐私政策</text>
      </view>
    </view>
  </view>

  <!-- 底部装饰 -->
  <view class="footer-decoration">
    <view class="decoration-text">人人读书 · 人人分享 · 人人成长</view>
  </view>
</view> 