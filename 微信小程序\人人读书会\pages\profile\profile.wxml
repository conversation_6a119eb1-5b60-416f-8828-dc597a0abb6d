<!--pages/profile/profile.wxml-->
<view class="profile-container" style="overflow: hidden;">
  <!-- 顶部导航 -->
  <view class="top-nav">
    <view class="nav-content">
    </view>
  </view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <!-- 背景装饰 -->
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>

      <view class="user-content">
        <!-- 用户头像和基本信息 -->
        <view class="user-header">
          <image class="user-avatar" src="{{userInfo.avatar}}" mode="aspectFill" />
          <view class="user-info">
            <text class="user-name">{{userInfo.nickname}}</text>
            <view class="user-stats">
              <view class="stat-item">
                <text class="stat-value">{{userInfo.likes}}</text>
                <text class="stat-label">赞和收藏</text>
              </view>
              <view class="stat-item">
                <text class="stat-value">{{userInfo.credits}}</text>
                <text class="stat-label">信用分</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 个人简介 -->
        <view class="user-bio">
          <text>{{userInfo.bio}}</text>
        </view>
      </view>
    </view>

    <!-- 功能标签栏 -->
    <view class="tabs-section">
      <view class="tabs-row">
        <view 
          wx:for="{{tabs}}" 
          wx:key="key"
          class="tab-item {{activeTab === item.key ? 'active' : ''}}"
          data-key="{{item.key}}"
          bindtap="switchTab"
        >
          <text class="tab-text">{{item.label}}</text>
          <view wx:if="{{activeTab === item.key}}" class="tab-line"></view>
        </view>
      </view>
    </view>

    <!-- 活动状态筛选 -->
    <view class="status-filter">
      <view class="filter-row">
        <view 
          wx:for="{{statusTabs}}" 
          wx:key="key"
          class="filter-item {{activeStatus === item.key ? 'active' : ''}}"
          data-key="{{item.key}}"
          bindtap="switchStatus"
        >
          {{item.label}}
        </view>
      </view>
    </view>

    <!-- 活动列表（可滚动区域） -->
    <scroll-view 
      scroll-y 
      class="activities-scroll"
      enhanced
      show-scrollbar="{{false}}"
    >
      <view class="activities-list">
        <view 
          wx:for="{{activities}}" 
          wx:key="id" 
          class="activity-item"
          data-id="{{item.id}}"
          bindtap="viewActivity"
        >
          <view class="activity-content">
            <!-- 活动封面 -->
            <image class="activity-cover" src="{{item.cover}}" mode="aspectFill" />

            <!-- 活动信息 -->
            <view class="activity-info">
              <view class="activity-header">
                <text class="activity-title">{{item.title}}</text>
                <text class="activity-type">{{item.type}}</text>
              </view>

              <view class="activity-details">
                <view class="detail-row">
                  <text class="detail-icon">⏰</text>
                  <text class="detail-text">{{item.time}}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-icon">📹</text>
                  <text class="detail-text">{{item.platform}}</text>
                </view>
                <view class="detail-row">
                  <text class="detail-icon">👥</text>
                  <text class="detail-text">报名 {{item.participants}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view> 