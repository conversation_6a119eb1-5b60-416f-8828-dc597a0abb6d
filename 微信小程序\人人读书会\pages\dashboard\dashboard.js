// pages/dashboard/dashboard.js
Page({
  data: {
    welcomeText: '早上好，书友',
    activeTimeFilter: 'upcoming',
    activeContentFilter: 'all',
    timeFilters: [
      { key: 'upcoming', label: '分会活动' },
      { key: 'history', label: '历史活动' }
    ],
    contentFilters: [
      { key: 'book', label: '书籍讨论' },
      { key: 'reading', label: '人人主讲' },
      { key: 'expert', label: '大咖精讲' },
      { key: 'topic', label: '话题研讨' }
    ],
    mockActivities: [
      {
        id: 1,
        title: '《乔布斯·禅与投资》·一响读书会·2025年第30期·线上',
        status: '线上',
        time: '07/25 20:00 周五',
        platform: '腾讯会议',
        participants: '10/11',
        coverImage: 'https://readdy.ai/api/search-image?query=Steve%20Jobs%20book%20cover%2C%20zen%20and%20investment%20theme%2C%20minimalist%20design%2C%20book%20photography%20style%2C%20soft%20lighting%2C%20clean%20background%2C%20professional%20business%20book%20cover&width=100&height=140&seq=book1&orientation=portrait'
      },
      {
        id: 2,
        title: '毛选第六次分享会 星星之火，可以燎原',
        status: '线上',
        time: '07/25 20:00 周五',
        platform: '腾讯会议',
        participants: '13/13',
        coverImage: 'https://readdy.ai/api/search-image?query=Classic%20Chinese%20book%20cover%2C%20red%20and%20gold%20theme%2C%20traditional%20Chinese%20calligraphy%20style%2C%20historical%20literature%2C%20elegant%20book%20design%2C%20soft%20lighting&width=100&height=140&seq=book2&orientation=portrait'
      },
      {
        id: 3,
        title: '每日复盘 | 2025年第29周',
        status: '线上',
        time: '07/25 20:00 周五',
        platform: '腾讯会议',
        participants: '15/20',
        coverImage: 'https://readdy.ai/api/search-image?query=Weekly%20review%20book%20cover%2C%20modern%20business%20design%2C%20colorful%20gradient%20background%2C%20productivity%20theme%2C%20clean%20minimal%20design%2C%20soft%20shadows&width=100&height=140&seq=book3&orientation=portrait'
      }
    ]
  },

  onLoad() {
    // 设置欢迎语
    this.setWelcomeMessage();
  },

  onShow() {
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      });
    }
  },

  // 设置欢迎语
  setWelcomeMessage() {
    const hour = new Date().getHours();
    let greeting = '早上好';
    if (hour >= 12 && hour < 18) {
      greeting = '下午好';
    } else if (hour >= 18) {
      greeting = '晚上好';
    }
    
    const userInfo = wx.getStorageSync('userInfo');
    const nickname = userInfo?.nickName || '书友';
    
    this.setData({
      welcomeText: `${greeting}，${nickname}`
    });
  },

  // 切换时间筛选
  switchTimeFilter(e) {
    const key = e.currentTarget.dataset.key;
    this.setData({
      activeTimeFilter: key
    });
  },

  // 切换内容筛选
  switchContentFilter(e) {
    const key = e.currentTarget.dataset.key;
    this.setData({
      activeContentFilter: key
    });
  },

  // 查看活动详情
  viewActivityDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '活动详情页面开发中',
      icon: 'none'
    });
  },

  // 成为会员
  becomeMember() {
    wx.showModal({
      title: '成为会员',
      content: '会员功能即将开放，敬请期待！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 发起活动
  createActivity() {
    wx.showModal({
      title: '发起活动',
      content: '发起活动功能即将开放，敬请期待！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 建立分会
  createBranch() {
    wx.showModal({
      title: '建立分会',
      content: '建立分会功能即将开放，敬请期待！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 发布活动（中间按钮）
  publishActivity() {
    wx.showModal({
      title: '发布活动',
      content: '发布活动功能即将开放，敬请期待！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setWelcomeMessage();
    
    // 模拟刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success',
        duration: 1000
      });
    }, 1000);
  }
}); 