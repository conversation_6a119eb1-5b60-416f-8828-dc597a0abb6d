<!--pages/dashboard/dashboard.wxml-->
<view class="dashboard-container" style="overflow: hidden;">
  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 功能卡片区域 -->
    <view class="feature-section">
      <view class="feature-grid">
        <!-- 成为会员 -->
        <view class="feature-card" bindtap="becomeMember" hover-class="card-hover">
          <view class="feature-icon vip-icon">
            <text class="icon-text">👑</text>
          </view>
          <text class="feature-text">成为会员</text>
        </view>

        <!-- 发起活动 -->
        <view class="feature-card" bindtap="createActivity" hover-class="card-hover">
          <view class="feature-icon activity-icon">
            <text class="icon-text">📅</text>
          </view>
          <text class="feature-text">发起活动</text>
        </view>

        <!-- 建立分会 -->
        <view class="feature-card" bindtap="createBranch" hover-class="card-hover">
          <view class="feature-icon branch-icon">
            <text class="icon-text">🏛️</text>
          </view>
          <text class="feature-text">成为分会长</text>
        </view>
      </view>
    </view>



    <!-- 内容筛选标签 -->
    <view class="content-filter-section">
      <scroll-view scroll-x class="content-filter-scroll" show-scrollbar="{{false}}">
        <view class="content-filter-container">
          <view 
            wx:for="{{contentFilters}}" 
            wx:key="key"
            class="content-filter-item {{activeContentFilter === item.key ? 'active' : ''}}"
            data-key="{{item.key}}"
            bindtap="switchContentFilter"
            hover-class="filter-hover"
          >
            {{item.label}}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 活动内容列表 -->
    <scroll-view scroll-y class="activity-scroll" enhanced show-scrollbar="{{false}}">
      <view class="activity-content">
        <!-- 日期标题 -->
        <view class="date-header">
          <text class="date-number">07</text>
          <view class="date-info">
            <text class="date-text">月25日 周五</text>
          </view>
        </view>

        <!-- 活动卡片列表 -->
        <view class="activity-list">
          <view 
            wx:for="{{mockActivities}}" 
            wx:key="id" 
            class="activity-item" 
            data-id="{{item.id}}" 
            bindtap="viewActivityDetail"
            hover-class="activity-hover"
          >
            <!-- 活动封面 -->
            <view class="activity-cover-wrapper">
              <image class="activity-cover" src="{{item.coverImage}}" mode="aspectFill" />
            </view>

            <!-- 活动信息 -->
            <view class="activity-info">
              <text class="activity-title">{{item.title}}</text>

              <view class="activity-status-badge">{{item.status}}</view>

              <view class="activity-details">
                <view class="detail-row">
                  <text class="detail-icon">⏰</text>
                  <text class="detail-text">时间：{{item.time}}</text>
                </view>

                <view class="detail-row">
                  <text class="detail-icon">📹</text>
                  <text class="detail-text">平台：{{item.platform}}</text>
                </view>

                <view class="detail-row">
                  <text class="detail-icon">👤</text>
                  <text class="detail-text">报名：{{item.participants}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view> 