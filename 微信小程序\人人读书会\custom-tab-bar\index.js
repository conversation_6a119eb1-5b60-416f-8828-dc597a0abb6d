// custom-tab-bar/index.js
Component({
  data: {
    selected: 0,
    color: "#6b7280",
    selectedColor: "#ef4444",
    list: [
      {
        pagePath: "/pages/dashboard/dashboard",
        text: "首页",
        iconPath: "/images/home.png",
        selectedIconPath: "/images/home-active.png"
      },
      {
        pagePath: "/pages/service/service",
        text: "服务会",
        iconPath: "/images/service.png",
        selectedIconPath: "/images/service-active.png"
      },
      {
        pagePath: "/pages/checkin/checkin",
        text: "签到",
        iconPath: "/images/checkin.png",
        selectedIconPath: "/images/checkin-active.png"
      },
      {
        pagePath: "/pages/profile/profile",
        text: "我的",
        iconPath: "/images/profile.png",
        selectedIconPath: "/images/profile-active.png"
      }
    ]
  },
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    },
    
    // 发布按钮点击事件
    onPublish() {
      wx.showModal({
        title: '发布活动',
        content: '发布活动功能即将开放，敬请期待！',
        showCancel: false,
        confirmText: '我知道了'
      });
    }
  }
}); 