/* pages/login/login.wxss */

/* 重置按钮默认样式 */
button {
  padding: 0;
  margin: 0;
  background: none;
  border: none;
  outline: none;
  -webkit-appearance: none;
}

button::after {
  border: none;
  display: none;
}

.login-container {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 渐变背景 */
.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  z-index: -1;
}

/* 登录内容区域 */
.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  position: relative;
  z-index: 1;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.logo-wrapper {
  width: 180rpx;
  height: 180rpx;
  margin: 0 auto 40rpx;
  border-radius: 40rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
  background: white;
  padding: 20rpx;
}

.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
}

.app-title {
  display: block;
  font-size: 52rpx;
  font-weight: bold;
  color:#ef4444;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.app-subtitle {
  display: block;
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 48rpx;
  padding: 64rpx;
  box-shadow: 0 30rpx 80rpx rgba(0, 0, 0, 0.15);
  border: none;
  outline: none;
}

/* 微信登录按钮 */
.wechat-login-btn {
  width: 200%;
 
  height: 100rpx;
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  border-radius: 50rpx;
  border: none;
  color: white;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  margin: 0 auto 32rpx auto;
  box-shadow: 0 10rpx 30rpx rgba(7, 193, 96, 0.3);
  outline: none;
}

/* 去除按钮的默认样式 */
.wechat-login-btn::after {
  border: none;
  display: none;
}

.wechat-login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 5rpx 20rpx rgba(7, 193, 96, 0.3);
}

.wechat-login-btn.loading {
  opacity: 0.8;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.wechat-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.btn-text {
  color: white;
  font-size: 32rpx;
  font-weight: 500;
}

/* 协议说明 */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  font-size: 24rpx;
}

.agreement-text {
  color: #6b7280;
  margin: 0 8rpx;
}

.agreement-link {
  color: #ef4444;
  margin: 0 8rpx;
  font-weight: 500;
}

/* 底部装饰 */
.footer-decoration {
  padding: 0rpx 0 150rpx 0;
  text-align: center;
  position: relative;
}

.decoration-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 4rpx;
}

/* 适配iPhone X等全面屏 */
.login-container {
  padding-bottom: env(safe-area-inset-bottom);
  padding-top: env(safe-area-inset-top);
} 